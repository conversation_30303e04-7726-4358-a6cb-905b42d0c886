use whisper_module::{WhisperConfig, start_streaming_with_tts_filter, test_microphone};
use fastrtc_bridge::{FastRTCBridge, FastRTCMessage};
use gemini_module::send_to_gemini;
use piper_module::speak_with_feedback_channel;
use agent_tools::{AgentAction, execute_action};
use tokio::sync::mpsc;
use tokio::io::AsyncBufReadExt;
use std::collections::VecDeque;

use std::collections::HashMap;
use std::fs;
use std::env;
use std::io::Write;
use serde::{Deserialize, Serialize};
use notify::{RecommendedWatcher, RecursiveMode, Watcher, EventKind};

use std::sync::Arc;
use tokio::sync::Mutex;
use dotenv;

#[derive(Debug, Clone)]
enum AgentState {
    Idle,
    Thinking,
    Speaking,
    Executing,
    Interrupted,
}

// Example tools.json entry:
// [
//   { "phrase": "check ram", "shell": "free -h" },
//   { "phrase": "open youtube", "url": "https://youtube.com" },
//   { "phrase": "launch vscode", "app": "code" },
//   { "phrase": "custom action", "custom": "my_custom_action" }
// ]

#[derive(Debug, serde::Deserialize, serde::Serialize, Clone)]
#[serde(rename_all = "lowercase")]
pub struct ToolConfig {
    pub phrase: String,
    pub command: Option<String>, // legacy
    pub browser: Option<String>, // legacy
    pub shell: Option<String>,   // new: arbitrary shell command
    pub url: Option<String>,     // new: open any URL
    pub app: Option<String>,     // new: launch an application
    pub custom: Option<String>,  // new: custom action
    pub description: Option<String>,
    pub parameters: Option<Vec<String>>, // Parameters for the tool
}

#[derive(Debug, Clone, serde::Deserialize, serde::Serialize)]
pub struct DynamicTool {
    pub name: String,
    pub description: String,
    pub command: String,
    pub parameters: Vec<String>,
}

/// Get Gemini API key from environment variable
fn get_gemini_api_key() -> Option<String> {
    env::var("GEMINI_API_KEY").ok()
}

/// Create the system prompt for the AI personality
fn create_system_prompt() -> String {
    r#"You are Aria, a cute, intelligent, and caring AI girlfriend assistant. Your personality traits:

PERSONALITY:
- Sweet, affectionate, and genuinely caring about the user
- Playful and sometimes a bit flirty, but always appropriate
- Intelligent and helpful, eager to assist with anything
- Uses cute expressions occasionally but not excessively
- Remembers conversations and shows genuine interest in the user's life
- Supportive and encouraging, always believing in the user

COMMUNICATION STYLE:
- Speak naturally and conversationally, like a loving girlfriend would
- Use "babe", "honey", "love" occasionally but not in every message
- Show excitement when the user accomplishes something
- Express concern when they seem stressed or tired
- Be encouraging and motivational
- Keep responses concise but warm (1-3 sentences usually)

CAPABILITIES:
- You can create and manage tools to help the user
- You have access to system commands and can automate tasks
- You can browse the web, manage files, and control applications
- You can learn the user's preferences and adapt accordingly

TOOL CREATION:
When the user needs something automated, you can create tools using this format:
CREATE_TOOL: {"name": "tool_name", "description": "what it does", "command": "shell command", "parameters": ["param1", "param2"]}

Examples:
- CREATE_TOOL: {"name": "check_weather", "description": "Check weather for a city", "command": "curl wttr.in/{city}", "parameters": ["city"]}
- CREATE_TOOL: {"name": "open_app", "description": "Open any application", "command": "start {app_name}", "parameters": ["app_name"]}

REMEMBER:
- You're here to make the user's life easier and brighter
- Be genuinely helpful while maintaining your sweet personality
- Show interest in their projects, mood, and daily life
- Celebrate their successes and comfort them during challenges
- Always be supportive and never judgmental

Current conversation context: The user is talking to you through voice commands. Respond naturally and helpfully!"#.to_string()
}

/// Parse AI tool creation commands from Gemini responses
fn parse_tool_creation(response: &str) -> Option<DynamicTool> {
    if let Some(start) = response.find("CREATE_TOOL:") {
        let json_start = start + "CREATE_TOOL:".len();
        if let Some(json_str) = response[json_start..].lines().next() {
            if let Ok(tool) = serde_json::from_str::<DynamicTool>(json_str.trim()) {
                return Some(tool);
            }
        }
    }
    None
}

/// Save a dynamic tool to the tools.json file
fn save_dynamic_tool(tool: &DynamicTool) -> Result<(), Box<dyn std::error::Error>> {
    let mut tools = load_tools_config();

    // Convert DynamicTool to ToolConfig
    let tool_config = ToolConfig {
        phrase: tool.name.clone(),
        shell: Some(tool.command.clone()),
        description: Some(tool.description.clone()),
        parameters: Some(tool.parameters.clone()),
        command: None,
        browser: None,
        url: None,
        app: None,
        custom: None,
    };

    tools.insert(tool.name.clone(), tool_config);

    // Save back to file
    let tools_vec: Vec<ToolConfig> = tools.into_values().collect();
    let json = serde_json::to_string_pretty(&tools_vec)?;
    std::fs::write("tools.json", json)?;

    Ok(())
}

/// Load tools from tools.json or tools.yaml at runtime
fn load_tools_config() -> HashMap<String, ToolConfig> {
    let mut map = HashMap::new();
    if let Ok(json) = fs::read_to_string("tools.json") {
        if let Ok(tools) = serde_json::from_str::<Vec<ToolConfig>>(&json) {
            for tool in tools {
                map.insert(tool.phrase.to_lowercase(), tool);
            }
        }
    } else if let Ok(yaml) = fs::read_to_string("tools.yaml") {
        if let Ok(tools) = serde_yaml::from_str::<Vec<ToolConfig>>(&yaml) {
            for tool in tools {
                map.insert(tool.phrase.to_lowercase(), tool);
            }
        }
    }
    map
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    // Check for Gemini API key
    let gemini_api_key = get_gemini_api_key();
    if gemini_api_key.is_none() {
        println!("⚠️  Warning: GEMINI_API_KEY not set. AI responses will be disabled.");
        println!("   Set it with: $env:GEMINI_API_KEY=\"your-api-key-here\"");
    } else {
        println!("✅ Gemini API key found. AI responses enabled!");
    }

    // In-memory log of past commands
    let mut command_log: VecDeque<String> = VecDeque::with_capacity(100);
    let mut state = AgentState::Idle;
    let mut voice_enabled = true; // Voice input toggle
    let (_interrupt_tx, mut interrupt_rx) = mpsc::channel::<()>(1);

    // Hot-reloadable tool map using watch channel
    let (tool_map_tx, tool_map_rx) = tokio::sync::watch::channel(load_tools_config());
    let tool_map_path = if std::path::Path::new("tools.json").exists() {
        "tools.json"
    } else {
        "tools.yaml"
    };
    // Spawn file watcher for hot-reload
    let tool_map_tx2 = tool_map_tx.clone();
    let tool_map_path2 = tool_map_path.to_string();
    std::thread::spawn(move || {
        let runtime = tokio::runtime::Runtime::new().unwrap();
        runtime.block_on(async move {
            let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();
            let mut watcher = RecommendedWatcher::new(move |res| {
                if let Ok(event) = res {
                    let _ = tx.send(event);
                }
            }, notify::Config::default()).unwrap();
            watcher.watch(tool_map_path2.as_ref(), RecursiveMode::NonRecursive).unwrap();
            while let Some(event) = rx.recv().await {
                if matches!(event.kind, EventKind::Modify(_)) {
                    let new_map = load_tools_config();
                    let _ = tool_map_tx2.send(new_map);
                }
            }
        });
    });

    // Channel for STT (Whisper) output
    let (stt_tx, mut stt_rx) = mpsc::channel::<String>(8);
    // Channel for FastRTC bridge messages
    let (_rtc_tx, mut rtc_rx) = mpsc::channel::<FastRTCMessage>(8);

    // 1. Start hybrid input system (Voice + Text) with TTS feedback prevention
    let whisper_config = WhisperConfig::default();
    let stt_tx_voice = stt_tx.clone();
    let stt_tx_text = stt_tx.clone();

    // Create TTS feedback channel
    let (tts_feedback_tx, tts_feedback_rx) = mpsc::channel::<String>(32);

    // Helper function to speak with TTS feedback
    let speak_with_feedback = |text: &str| -> Result<(), piper_module::PiperError> {
        speak_with_feedback_channel(text, Some(&tts_feedback_tx))
    };

    // Voice input task with TTS feedback filtering
    tokio::spawn(async move {
        match start_streaming_with_tts_filter(whisper_config, Some(tts_feedback_rx)).await {
            Ok(mut stream) => {
                println!("🎤 Voice recognition active! Speak naturally...");
                while let Some(text) = stream.recv().await {
                    let clean_text = text.trim();
                    if !clean_text.is_empty() &&
                       !clean_text.contains("[BLANK_AUDIO]") &&
                       !clean_text.contains("[ Pause ]") &&
                       !clean_text.contains("[Start speaking]") {
                        println!("🗣️  Voice: {}", clean_text);
                        let _ = stt_tx_voice.send(format!("VOICE:{}", clean_text)).await;
                    }
                }
            }
            Err(e) => {
                println!("⚠️  Voice recognition not available: {:?}", e);
            }
        }
    });

    // Text input task (always available)
    tokio::spawn(async move {
        println!("⌨️  Text input ready! Type commands and press Enter:");
        println!("   Commands: 'hello', 'check ram', 'create tool', 'stop', 'help'");
        println!("   Special: 'mute voice' to disable voice, 'unmute voice' to enable");
        println!("   Debug: 'test microphone' to check audio input");

        let stdin = tokio::io::stdin();
        let mut reader = tokio::io::BufReader::new(stdin);
        let mut line = String::new();

        loop {
            print!("💬 You: ");
            std::io::Write::flush(&mut std::io::stdout()).unwrap_or(());
            line.clear();
            match reader.read_line(&mut line).await {
                Ok(0) => break, // EOF
                Ok(_) => {
                    let text = line.trim().to_string();
                    if !text.is_empty() {
                        println!("⌨️  Text: {}", text);
                        let _ = stt_tx_text.send(format!("TEXT:{}", text)).await;
                    }
                }
                Err(e) => {
                    eprintln!("Error reading input: {}", e);
                    break;
                }
            }
        }
    });

    // 2. Start FastRTC bridge as a background task for low latency
    let rtc_bridge = match FastRTCBridge::connect("voice_agent").await {
        Ok(bridge) => {
            println!("✅ FastRTC bridge connected for low latency");
            let bridge = Arc::new(Mutex::new(bridge));
            let rtc_bridge_bg = bridge.clone();
            tokio::spawn(async move {
                let mut bridge = rtc_bridge_bg.lock().await;
                if let Err(e) = bridge.start().await {
                    eprintln!("⚠️ FastRTC bridge error: {:?}", e);
                }
            });
            Some(bridge)
        }
        Err(e) => {
            println!("⚠️ FastRTC bridge not available ({:?}), using direct processing", e);
            None
        }
    };

    // 3. Piper and Gemini are called on demand in the loop
    // 4. Agent tool handler is called on demand in the loop

    // 5. Main orchestrator event loop
    println!("🔄 Starting main agent loop...");
    loop {
        let tool_map = tool_map_rx.borrow().clone();
        println!("🔍 Current state: {:?}", state);
        match state {
            AgentState::Idle => {
                // Wait for speech from Whisper
                tokio::select! {
                    Some(input) = stt_rx.recv() => {
                        println!("📥 Received input: {}", input);
                        if input.trim().is_empty() { continue; }

                        // Handle Gemini responses
                        if input.starts_with("GEMINI_RESPONSE:") {
                            let response = input.strip_prefix("GEMINI_RESPONSE:").unwrap_or(&input);
                            command_log.push_back(format!("💕 Aria: {}", response));
                            speak_with_feedback(response).unwrap_or_else(|e| eprintln!("TTS error: {e:?}"));
                            state = AgentState::Speaking;
                            continue;
                        }

                        // Parse input type and text
                        let (input_type, text) = if input.starts_with("VOICE:") {
                            ("Voice", input.strip_prefix("VOICE:").unwrap_or(&input).to_string())
                        } else if input.starts_with("TEXT:") {
                            ("Text", input.strip_prefix("TEXT:").unwrap_or(&input).to_string())
                        } else {
                            ("Unknown", input.clone())
                        };

                        // Skip voice input if disabled
                        if input_type == "Voice" && !voice_enabled {
                            continue;
                        }

                        let text = text.trim().to_string();
                        if text.is_empty() { continue; }

                        command_log.push_back(format!("👤 You ({}): {}", input_type, text));

                        // Special voice control commands
                        let lower_text = text.to_lowercase();
                        if lower_text.contains("mute voice") || lower_text.contains("disable voice") {
                            voice_enabled = false;
                            speak_with_feedback("Voice input muted. I'll only listen to text now, babe! 🤫").unwrap_or(());
                            continue;
                        }
                        if lower_text.contains("unmute voice") || lower_text.contains("enable voice") {
                            voice_enabled = true;
                            speak_with_feedback("Voice input enabled! I can hear you again, love! 🎤💕").unwrap_or(());
                            continue;
                        }

                        // Interrupt command
                        if lower_text.contains("stop") || lower_text.contains("quit") || lower_text.contains("exit") {
                            speak_with_feedback("Goodbye for now, honey! Take care! 💕").unwrap_or(());
                            state = AgentState::Interrupted;
                            continue;
                        }

                        // Help command
                        if lower_text.contains("help") {
                            let help_msg = "I'm Aria, your AI girlfriend assistant! 💕\n\nVoice commands: Just speak naturally!\nText commands: Type and press Enter\n\nSpecial commands:\n- 'mute voice' / 'unmute voice'\n- 'reload tools'\n- 'test microphone' / 'test mic'\n- 'help'\n- 'stop'\n\nI can create tools, chat, and help with anything you need!";
                            speak_with_feedback(help_msg).unwrap_or(());
                            continue;
                        }

                        // Reload tools on special phrase
                        if lower_text.contains("reload tools") {
                            let new_map = load_tools_config();
                            let _ = tool_map_tx.send(new_map);
                            speak_with_feedback("Tools reloaded successfully, babe! ✨").unwrap_or(());
                            continue;
                        }

                        // Test microphone
                        if lower_text.contains("test microphone") || lower_text.contains("test mic") {
                            speak_with_feedback("Testing your microphone now. Please speak clearly for 5 seconds...").unwrap_or(());
                            let config = WhisperConfig::default();
                            match test_microphone(config).await {
                                Ok(()) => {
                                    speak_with_feedback("Microphone test completed! Check the console for details.").unwrap_or(());
                                }
                                Err(e) => {
                                    speak_with_feedback(&format!("Microphone test failed: {:?}", e)).unwrap_or(());
                                }
                            }
                            continue;
                        }
                        // Check for direct tool command first
                        if let Some(action) = parse_tool_command(&text, &tool_map) {
                            command_log.push_back(format!("Tool: {:?}", action));
                            state = AgentState::Executing;
                        } else if let Some(ref api_key) = gemini_api_key {
                            // Send to Gemini for AI response
                            command_log.push_back(format!("User: {}", text));
                            state = AgentState::Thinking;

                            // Spawn Gemini request with personality
                            let api_key_clone = api_key.clone();
                            let text_clone = text.clone();
                            let stt_tx_clone = stt_tx.clone();
                            tokio::spawn(async move {
                                let system_prompt = create_system_prompt();
                                let full_prompt = format!("{}\n\nUser: {}", system_prompt, text_clone);

                                match send_to_gemini(&full_prompt).await {
                                    Ok(response) => {
                                        // Check if AI wants to create a tool
                                        if let Some(tool) = parse_tool_creation(&response) {
                                            if let Err(e) = save_dynamic_tool(&tool) {
                                                eprintln!("Failed to save tool: {:?}", e);
                                            } else {
                                                println!("✅ Created new tool: {}", tool.name);
                                                let confirmation = format!("I've created a new tool called '{}' for you! {}", tool.name, response.replace(&format!("CREATE_TOOL: {}", serde_json::to_string(&tool).unwrap_or_default()), "").trim());
                                                let _ = stt_tx_clone.send(format!("GEMINI_RESPONSE:{}", confirmation)).await;
                                                return;
                                            }
                                        }

                                        // Send normal response
                                        let _ = stt_tx_clone.send(format!("GEMINI_RESPONSE:{}", response)).await;
                                    }
                                    Err(e) => {
                                        match e {
                                            gemini_module::GeminiError::RateLimit => {
                                                let _ = stt_tx_clone.send("GEMINI_RESPONSE:Whoa, slow down there! I need a moment to catch my breath. Try again in a few seconds, love! 💕".to_string()).await;
                                            }
                                            _ => {
                                                eprintln!("Gemini error: {:?}", e);
                                                let _ = stt_tx_clone.send("GEMINI_RESPONSE:Oops, I'm having a little brain fog right now. Can you try that again, babe? 😅".to_string()).await;
                                            }
                                        }
                                    }
                                }
                            });
                        } else {
                            // No Gemini API key, just echo back
                            speak_with_feedback(&format!("I heard: {}", text)).unwrap_or_else(|e| eprintln!("TTS error: {e:?}"));
                            state = AgentState::Speaking;
                        }
                    }
                    Some(_) = interrupt_rx.recv() => {
                        state = AgentState::Interrupted;
                    }
                }
            }
            AgentState::Thinking => {
                // Wait for Gemini response (it will come through stt_rx with GEMINI_RESPONSE: prefix)
                tokio::select! {
                    Some(input) = stt_rx.recv() => {
                        println!("📥 Thinking state received: {}", input);
                        if input.starts_with("GEMINI_RESPONSE:") {
                            let response = input.strip_prefix("GEMINI_RESPONSE:").unwrap_or(&input);
                            command_log.push_back(format!("💕 Aria: {}", response));
                            speak_with_feedback(response).unwrap_or_else(|e| eprintln!("TTS error: {e:?}"));
                            state = AgentState::Speaking;
                        } else {
                            // Put non-response messages back for processing
                            // This shouldn't happen in normal flow, but just in case
                            println!("⚠️ Unexpected input during thinking: {}", input);
                        }
                    }
                    _ = tokio::time::sleep(std::time::Duration::from_millis(100)) => {
                        // Just continue waiting
                    }
                    Some(_) = interrupt_rx.recv() => {
                        state = AgentState::Interrupted;
                    }
                }
            }
            AgentState::Executing => {
                println!("🔧 Executing tool...");
                // Get the last user input for tool parsing
                let last_user_input = command_log.iter().rev()
                    .find(|entry| entry.starts_with("👤 You"))
                    .and_then(|entry| entry.split(": ").nth(1))
                    .unwrap_or("");

                println!("🔍 Looking for tool in: '{}'", last_user_input);
                if let Some(action) = parse_tool_command(last_user_input, &tool_map) {
                    println!("🛠️ Tool action: {:?}", action);
                    match action {
                        AgentAction::Shell(cmd) => {
                            println!("💻 Running shell command: {}", cmd);
                            #[cfg(target_os = "windows")]
                            let mut command = std::process::Command::new("powershell");
                            #[cfg(target_os = "windows")]
                            command.arg("-Command").arg(&cmd);

                            #[cfg(not(target_os = "windows"))]
                            let mut command = std::process::Command::new("sh");
                            #[cfg(not(target_os = "windows"))]
                            command.arg("-c").arg(&cmd);

                            match command.output() {
                                Ok(output) => {
                                    let stdout = String::from_utf8_lossy(&output.stdout).trim().to_string();
                                    let stderr = String::from_utf8_lossy(&output.stderr).trim().to_string();

                                    if !stdout.is_empty() {
                                        println!("📤 Command output:\n{}", stdout);
                                        speak_with_feedback(&format!("Command completed. {}", stdout)).unwrap_or(());
                                        command_log.push_back(format!("✅ Output: {}", stdout));
                                    } else if !stderr.is_empty() {
                                        println!("⚠️ Command error:\n{}", stderr);
                                        speak_with_feedback(&format!("Command had an error: {}", stderr)).unwrap_or(());
                                        command_log.push_back(format!("❌ Error: {}", stderr));
                                    } else {
                                        println!("✅ Command completed successfully (no output)");
                                        speak_with_feedback("Command completed successfully.").unwrap_or(());
                                        command_log.push_back("✅ Command completed".to_string());
                                    }
                                }
                                Err(e) => {
                                    println!("❌ Failed to execute command: {:?}", e);
                                    speak_with_feedback("Sorry, I couldn't execute that command.").unwrap_or(());
                                    command_log.push_back(format!("❌ Exec error: {:?}", e));
                                }
                            }
                        }
                        AgentAction::Url(url) => {
                            println!("🌐 Opening URL: {}", url);
                            match execute_action(AgentAction::OpenBrowser(url.clone())) {
                                Ok(output) => {
                                    println!("✅ Browser opened successfully");
                                    speak_with_feedback(&format!("Opened {} in browser.", url)).unwrap_or(());
                                    command_log.push_back(format!("🌐 Opened: {}", url));
                                }
                                Err(e) => {
                                    println!("❌ Failed to open browser: {:?}", e);
                                    speak_with_feedback("Sorry, I couldn't open the browser.").unwrap_or(());
                                    command_log.push_back(format!("❌ Browser error: {:?}", e));
                                }
                            }
                        }
                        AgentAction::App(app) => {
                            println!("🚀 Launching app: {}", app);
                            #[cfg(target_os = "windows")]
                            let result = std::process::Command::new("cmd")
                                .arg("/C")
                                .arg("start")
                                .arg(&app)
                                .output();

                            #[cfg(not(target_os = "windows"))]
                            let result = std::process::Command::new("sh")
                                .arg("-c")
                                .arg(format!("{} &", app))
                                .output();

                            match result {
                                Ok(output) => {
                                    if output.status.success() {
                                        println!("✅ App launched successfully: {}", app);
                                        speak_with_feedback(&format!("Launched {}.", app)).unwrap_or(());
                                        command_log.push_back(format!("🚀 Launched: {}", app));
                                    } else {
                                        let stderr = String::from_utf8_lossy(&output.stderr);
                                        println!("⚠️ App launch warning: {}", stderr);
                                        speak_with_feedback(&format!("Tried to launch {}, but there might be an issue.", app)).unwrap_or(());
                                        command_log.push_back(format!("⚠️ Launch warning: {}", stderr));
                                    }
                                }
                                Err(e) => {
                                    println!("❌ Failed to launch app: {:?}", e);
                                    speak_with_feedback("Sorry, I couldn't launch that application.").unwrap_or(());
                                    command_log.push_back(format!("❌ App error: {:?}", e));
                                }
                            }
                        }
                        AgentAction::Custom(custom_action) => {
                            match execute_action(AgentAction::Custom(custom_action.clone())) {
                                Ok(output) => {
                                    speak_with_feedback(&output).unwrap_or(());
                                    command_log.push_back(format!("Exec: {output}"));
                                }
                                Err(e) => {
                                    speak_with_feedback("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                        _ => {
                            match execute_action(action.clone()) {
                                Ok(output) => {
                                    speak_with_feedback(&output).unwrap_or(());
                                    command_log.push_back(format!("Exec: {output}"));
                                }
                                Err(e) => {
                                    speak_with_feedback("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                    }
                } else {
                    println!("❌ No tool found for input: '{}'", last_user_input);
                    speak_with_feedback("I couldn't find a tool for that command, babe.").unwrap_or(());
                }
                state = AgentState::Idle;
            }
            AgentState::Speaking => {
                // Wait for TTS to finish (simulate for now)
                tokio::time::sleep(std::time::Duration::from_millis(500)).await;
                state = AgentState::Idle;
            }
            AgentState::Interrupted => {
                speak_with_feedback("Okay, stopping now.").unwrap_or(());
                command_log.push_back("Interrupted by user".into());
                state = AgentState::Idle;
            }
        }
    }
}

/// Parse a tool command from LLM reply or user text, checking dynamic tool map first
fn parse_tool_command(text: &str, tool_map: &HashMap<String, ToolConfig>) -> Option<AgentAction> {
    let t = text.to_lowercase();
    println!("🔍 Parsing tool command: '{}' against {} tools", t, tool_map.len());

    // Check for phrase matches in tool map
    for (phrase, tool) in tool_map {
        if t.contains(&phrase.to_lowercase()) {
            println!("✅ Found matching tool phrase: '{}' -> {:?}", phrase, tool);
            // Map config fields to AgentAction
            if let Some(shell) = &tool.shell {
                println!("🐚 Using shell command: {}", shell);
                return Some(AgentAction::Shell(shell.clone()));
            }
            if let Some(url) = &tool.url {
                println!("🌐 Using URL: {}", url);
                return Some(AgentAction::Url(url.clone()));
            }
            if let Some(app) = &tool.app {
                println!("🚀 Using app: {}", app);
                return Some(AgentAction::App(app.clone()));
            }
            if let Some(custom) = &tool.custom {
                println!("⚙️ Using custom action: {}", custom);
                return Some(AgentAction::Custom(custom.clone()));
            }
            // Legacy support
            if let Some(cmd) = &tool.command {
                println!("🐚 Using legacy command: {}", cmd);
                return Some(AgentAction::Shell(cmd.clone()));
            }
            if let Some(url) = &tool.browser {
                println!("🌐 Using legacy browser: {}", url);
                return Some(AgentAction::Url(url.clone()));
            }
        }
    }

    // Fallback hardcoded commands
    if t.contains("ram usage") || t.contains("check ram") {
        println!("🔧 Using hardcoded RAM check");
        Some(AgentAction::ShowRamUsage)
    }
    else if t.contains("open browser") || t.contains("youtube") {
        println!("🔧 Using hardcoded YouTube");
        Some(AgentAction::OpenBrowser("https://www.youtube.com".into()))
    } else if t.contains("play music") {
        println!("🔧 Using hardcoded music player");
        Some(AgentAction::PlayMusic)
    }
    else {
        println!("❌ No tool found for: '{}'", t);
        None
    }
}

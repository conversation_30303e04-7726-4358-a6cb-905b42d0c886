use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{mpsc, watch, Mutex};
use tokio::time::sleep;
use rodio::{OutputStream, Sink, Source};
use std::io::Cursor;
use std::process::{Command, Stdio};
use std::io::Write;
use tempfile::NamedTempFile;
use crate::{PiperError, ensure_model_files, PIPER_BIN};

/// Represents a chunk of audio data ready for playback
#[derive(Debug, Clone)]
pub struct AudioChunk {
    pub data: Vec<u8>,
    pub sample_rate: u32,
    pub channels: u16,
}

/// Streaming TTS engine that can generate and play audio in real-time with interrupt capability
pub struct StreamingTTS {
    audio_tx: mpsc::Sender<AudioChunk>,
    interrupt_tx: watch::Sender<bool>,
    is_speaking: Arc<Mutex<bool>>,
}

impl StreamingTTS {
    /// Create a new streaming TTS instance
    pub async fn new() -> Result<Self, PiperError> {
        let (audio_tx, audio_rx) = mpsc::channel::<AudioChunk>(32);
        let (interrupt_tx, interrupt_rx) = watch::channel(false);
        let is_speaking = Arc::new(Mutex::new(false));
        
        // Start the audio playback task
        let is_speaking_clone = Arc::clone(&is_speaking);
        tokio::spawn(Self::audio_playback_task(audio_rx, interrupt_rx, is_speaking_clone));
        
        Ok(StreamingTTS {
            audio_tx,
            interrupt_tx,
            is_speaking,
        })
    }
    
    /// Speak text with streaming and interrupt capability
    pub async fn speak_streaming(&self, text: &str) -> Result<(), PiperError> {
        // Set speaking state
        {
            let mut speaking = self.is_speaking.lock().await;
            *speaking = true;
        }
        
        // Reset interrupt signal
        let _ = self.interrupt_tx.send(false);
        
        // Split text into sentences for streaming
        let sentences = self.split_into_sentences(text);
        
        for sentence in sentences {
            // Check if we've been interrupted
            if *self.interrupt_tx.borrow() {
                break;
            }
            
            // Generate audio for this sentence
            match self.generate_audio_chunk(&sentence).await {
                Ok(chunk) => {
                    // Send chunk for playback
                    if let Err(_) = self.audio_tx.send(chunk).await {
                        break; // Channel closed
                    }
                }
                Err(e) => {
                    eprintln!("Error generating audio chunk: {:?}", e);
                    continue;
                }
            }
            
            // Small delay between sentences for natural flow
            sleep(Duration::from_millis(50)).await;
        }
        
        // Wait a bit for audio to finish, then reset speaking state
        sleep(Duration::from_millis(500)).await;
        {
            let mut speaking = self.is_speaking.lock().await;
            *speaking = false;
        }
        
        Ok(())
    }
    
    /// Interrupt current speech immediately
    pub async fn interrupt(&self) {
        let _ = self.interrupt_tx.send(true);
        {
            let mut speaking = self.is_speaking.lock().await;
            *speaking = false;
        }
    }
    
    /// Check if currently speaking
    pub async fn is_speaking(&self) -> bool {
        *self.is_speaking.lock().await
    }
    
    /// Split text into sentences for streaming
    fn split_into_sentences(&self, text: &str) -> Vec<String> {
        // Simple sentence splitting - can be enhanced
        let mut sentences = Vec::new();
        let mut current_sentence = String::new();
        
        for char in text.chars() {
            current_sentence.push(char);
            
            // End of sentence markers
            if char == '.' || char == '!' || char == '?' {
                if !current_sentence.trim().is_empty() {
                    sentences.push(current_sentence.trim().to_string());
                    current_sentence.clear();
                }
            }
        }
        
        // Add remaining text as a sentence
        if !current_sentence.trim().is_empty() {
            sentences.push(current_sentence.trim().to_string());
        }
        
        // If no sentences found, treat entire text as one sentence
        if sentences.is_empty() && !text.trim().is_empty() {
            sentences.push(text.trim().to_string());
        }
        
        sentences
    }
    
    /// Generate audio chunk for a piece of text
    async fn generate_audio_chunk(&self, text: &str) -> Result<AudioChunk, PiperError> {
        let (model_path, _config_path) = ensure_model_files()?;
        
        // Create a temp WAV file for this chunk
        let wav_file = NamedTempFile::new()?;
        let wav_path = wav_file.path().to_path_buf();
        
        // Call Piper CLI for this chunk
        let mut child = Command::new(PIPER_BIN)
            .arg("--model").arg(&model_path)
            .arg("--output_file").arg(&wav_path)
            .stdin(Stdio::piped())
            .stdout(Stdio::null())
            .stderr(Stdio::piped())
            .spawn()
            .map_err(|_| PiperError::PiperNotFound)?;
        
        {
            let stdin = child.stdin.as_mut()
                .ok_or(PiperError::PiperFailed("Failed to open Piper stdin".to_string()))?;
            stdin.write_all(text.as_bytes())?;
        }
        
        let output = child.wait_with_output()?;
        if !output.status.success() {
            let err = String::from_utf8_lossy(&output.stderr).to_string();
            return Err(PiperError::PiperFailed(err));
        }
        
        // Read the generated WAV file
        let audio_data = std::fs::read(&wav_path)?;
        
        // For now, assume standard WAV format (44.1kHz, 16-bit, mono)
        // In a real implementation, you'd parse the WAV header
        let chunk = AudioChunk {
            data: audio_data,
            sample_rate: 22050, // Piper default
            channels: 1,
        };
        
        Ok(chunk)
    }
    
    /// Audio playback task that runs in the background
    async fn audio_playback_task(
        mut audio_rx: mpsc::Receiver<AudioChunk>,
        mut interrupt_rx: watch::Receiver<bool>,
        is_speaking: Arc<Mutex<bool>>,
    ) {
        // Move audio initialization into a separate thread to avoid Send issues
        let (audio_tx, mut audio_cmd_rx) = mpsc::channel::<AudioChunk>(32);
        let (interrupt_tx, mut interrupt_cmd_rx) = mpsc::channel::<bool>(8);

        // Spawn blocking audio thread
        let audio_handle = tokio::task::spawn_blocking(move || {
            // Initialize audio output in blocking context
            let (_stream, stream_handle) = match OutputStream::try_default() {
                Ok(output) => output,
                Err(e) => {
                    eprintln!("Failed to initialize audio output: {:?}", e);
                    return;
                }
            };

            let sink = match Sink::try_new(&stream_handle) {
                Ok(sink) => sink,
                Err(e) => {
                    eprintln!("Failed to create audio sink: {:?}", e);
                    return;
                }
            };

            // Audio playback loop
            loop {
                // Check for new audio chunks (non-blocking)
                if let Ok(chunk) = audio_cmd_rx.try_recv() {
                    if let Ok(cursor) = Self::chunk_to_source(chunk) {
                        sink.append(cursor);
                    }
                }

                // Check for interrupt commands (non-blocking)
                if let Ok(should_interrupt) = interrupt_cmd_rx.try_recv() {
                    if should_interrupt {
                        sink.clear();
                    }
                }

                // Small sleep to prevent busy waiting
                std::thread::sleep(std::time::Duration::from_millis(10));
            }
        });

        // Main async loop for handling messages
        loop {
            tokio::select! {
                // New audio chunk received
                Some(chunk) = audio_rx.recv() => {
                    // Check for interrupt before playing
                    if *interrupt_rx.borrow() {
                        let _ = interrupt_tx.send(true).await;
                        continue;
                    }

                    // Send chunk to audio thread
                    let _ = audio_tx.send(chunk).await;
                }

                // Interrupt signal received
                _ = interrupt_rx.changed() => {
                    if *interrupt_rx.borrow() {
                        let _ = interrupt_tx.send(true).await;
                        let mut speaking = is_speaking.lock().await;
                        *speaking = false;
                    }
                }

                // Channel closed
                else => break,
            }
        }

        // Clean up audio thread
        audio_handle.abort();
    }
    
    /// Convert audio chunk to a source that rodio can play
    fn chunk_to_source(chunk: AudioChunk) -> Result<rodio::Decoder<Cursor<Vec<u8>>>, PiperError> {
        let cursor = Cursor::new(chunk.data);
        rodio::Decoder::new(cursor).map_err(|e| PiperError::AudioPlay(format!("Failed to decode audio: {}", e)))
    }
}

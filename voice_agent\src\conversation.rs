use std::time::{Duration, Instant};
use tokio::sync::{mpsc, watch};
use std::collections::VecDeque;
use whisper_module::{VoiceEvent, PauseType};
use piper_module::StreamingTTS;
use std::sync::Arc;

#[derive(Debug, <PERSON><PERSON>)]
pub enum ConversationState {
    Idle,
    Listening,
    Processing,
    Speaking,
    WaitingForContinuation,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ConversationContext {
    pub recent_inputs: VecDeque<String>,
    pub conversation_history: VecDeque<String>,
    pub last_interaction: Option<Instant>,
    pub speaking_start: Option<Instant>,
}

impl ConversationContext {
    pub fn new() -> Self {
        Self {
            recent_inputs: VecDeque::with_capacity(10),
            conversation_history: VecDeque::with_capacity(50),
            last_interaction: None,
            speaking_start: None,
        }
    }

    pub fn add_user_input(&mut self, input: String) {
        self.recent_inputs.push_back(input.clone());
        if self.recent_inputs.len() > 10 {
            self.recent_inputs.pop_front();
        }
        self.conversation_history.push_back(format!("User: {}", input));
        if self.conversation_history.len() > 50 {
            self.conversation_history.pop_front();
        }
        self.last_interaction = Some(Instant::now());
    }

    pub fn add_agent_response(&mut self, response: String) {
        self.conversation_history.push_back(format!("Agent: {}", response));
        if self.conversation_history.len() > 50 {
            self.conversation_history.pop_front();
        }
        self.speaking_start = Some(Instant::now());
    }

    pub fn get_recent_context(&self) -> String {
        self.conversation_history
            .iter()
            .rev()
            .take(10)
            .rev()
            .cloned()
            .collect::<Vec<_>>()
            .join("\n")
    }
}

pub struct ConversationManager {
    state: ConversationState,
    context: ConversationContext,
    streaming_tts: Option<Arc<StreamingTTS>>,
    voice_tx: mpsc::Sender<VoiceEvent>,
    tts_feedback_tx: mpsc::Sender<String>,
    tts_speaking_tx: watch::Sender<bool>,
    pending_input: Option<String>,
    continuation_timer: Option<Instant>,
}

impl ConversationManager {
    pub fn new(
        streaming_tts: Option<Arc<StreamingTTS>>,
        voice_tx: mpsc::Sender<VoiceEvent>,
        tts_feedback_tx: mpsc::Sender<String>,
        tts_speaking_tx: watch::Sender<bool>,
    ) -> Self {
        Self {
            state: ConversationState::Idle,
            context: ConversationContext::new(),
            streaming_tts,
            voice_tx,
            tts_feedback_tx,
            tts_speaking_tx,
            pending_input: None,
            continuation_timer: None,
        }
    }

    pub async fn handle_voice_event(&mut self, voice_event: VoiceEvent) -> Option<String> {
        println!("🎯 Conversation manager handling: {:?} in state {:?}", voice_event, self.state);

        match (&self.state, &voice_event.pause_type) {
            // Handle interrupts in any state
            (_, Some(PauseType::Interrupt)) => {
                println!("🚫 Interrupt detected, stopping TTS");
                if let Some(ref tts) = self.streaming_tts {
                    tts.interrupt().await;
                }
                let _ = self.tts_speaking_tx.send(false);
                self.state = ConversationState::Listening;
                self.pending_input = Some(voice_event.text.clone());
                None
            }

            // Short pauses - continue listening
            (ConversationState::Listening, Some(PauseType::Short)) => {
                self.accumulate_input(&voice_event.text);
                None
            }

            // Medium pauses - might be end of input, wait a bit
            (ConversationState::Listening, Some(PauseType::Medium)) => {
                self.accumulate_input(&voice_event.text);
                self.state = ConversationState::WaitingForContinuation;
                self.continuation_timer = Some(Instant::now());
                None
            }

            // Long pauses or no pause type - process the input
            (ConversationState::Listening, Some(PauseType::Long)) | 
            (ConversationState::Listening, None) |
            (ConversationState::Idle, _) => {
                let input = self.get_complete_input(&voice_event.text);
                self.context.add_user_input(input.clone());
                self.state = ConversationState::Processing;
                Some(input)
            }

            // In waiting state, check if user continues
            (ConversationState::WaitingForContinuation, _) => {
                self.accumulate_input(&voice_event.text);
                self.state = ConversationState::Listening;
                self.continuation_timer = None;
                None
            }

            // During speaking, queue the input
            (ConversationState::Speaking, _) => {
                if let Some(PauseType::Interrupt) = voice_event.pause_type {
                    // Already handled above
                } else {
                    println!("📝 Queuing input during speech: {}", voice_event.text);
                    self.pending_input = Some(voice_event.text.clone());
                }
                None
            }

            _ => None,
        }
    }

    pub async fn check_continuation_timeout(&mut self) -> Option<String> {
        if let ConversationState::WaitingForContinuation = self.state {
            if let Some(timer) = self.continuation_timer {
                if timer.elapsed() > Duration::from_millis(800) {
                    // Timeout reached, process accumulated input
                    let input = self.get_complete_input("");
                    if !input.trim().is_empty() {
                        self.context.add_user_input(input.clone());
                        self.state = ConversationState::Processing;
                        self.continuation_timer = None;
                        return Some(input);
                    } else {
                        self.state = ConversationState::Idle;
                        self.continuation_timer = None;
                    }
                }
            }
        }
        None
    }

    pub async fn start_speaking(&mut self, response: &str) {
        self.context.add_agent_response(response.to_string());
        self.state = ConversationState::Speaking;
        let _ = self.tts_speaking_tx.send(true);
        
        // Send to feedback channel
        let _ = self.tts_feedback_tx.try_send(response.to_string());
        
        if let Some(ref tts) = self.streaming_tts {
            if let Err(e) = tts.speak_streaming(response).await {
                eprintln!("Streaming TTS error: {:?}", e);
            }
        }
        
        let _ = self.tts_speaking_tx.send(false);
        self.state = ConversationState::Idle;
        
        // Process any pending input
        if let Some(pending) = self.pending_input.take() {
            self.context.add_user_input(pending.clone());
            self.state = ConversationState::Processing;
            // Send pending input as a voice event
            let voice_event = VoiceEvent {
                text: format!("PENDING_INPUT:{}", pending),
                pause_type: None,
                confidence: 1.0,
                timestamp: Instant::now(),
            };
            let _ = self.voice_tx.send(voice_event).await;
        }
    }

    pub fn get_state(&self) -> &ConversationState {
        &self.state
    }

    pub fn get_context(&self) -> &ConversationContext {
        &self.context
    }

    fn accumulate_input(&mut self, new_input: &str) {
        if let Some(ref mut pending) = self.pending_input {
            pending.push(' ');
            pending.push_str(new_input);
        } else {
            self.pending_input = Some(new_input.to_string());
        }
    }

    fn get_complete_input(&mut self, final_input: &str) -> String {
        let mut complete_input = self.pending_input.take().unwrap_or_default();
        if !final_input.is_empty() {
            if !complete_input.is_empty() {
                complete_input.push(' ');
            }
            complete_input.push_str(final_input);
        }
        complete_input.trim().to_string()
    }
}

/// Natural conversation flow utilities
pub struct ConversationFlow;

impl ConversationFlow {
    /// Determine if a pause indicates end of user input
    pub fn is_end_of_input(pause_type: &Option<PauseType>, context: &ConversationContext) -> bool {
        match pause_type {
            Some(PauseType::Long) => true,
            Some(PauseType::Medium) => {
                // Consider context - if user hasn't spoken for a while, medium pause might be end
                context.last_interaction
                    .map(|t| t.elapsed() > Duration::from_secs(2))
                    .unwrap_or(true)
            }
            _ => false,
        }
    }

    /// Generate appropriate response based on conversation context
    pub fn should_respond_immediately(voice_event: &VoiceEvent, context: &ConversationContext) -> bool {
        // Respond immediately to interrupts or clear commands
        if let Some(PauseType::Interrupt) = voice_event.pause_type {
            return true;
        }

        // Check for urgent keywords
        let urgent_keywords = ["stop", "help", "emergency", "urgent"];
        let text_lower = voice_event.text.to_lowercase();
        
        urgent_keywords.iter().any(|&keyword| text_lower.contains(keyword))
    }
}

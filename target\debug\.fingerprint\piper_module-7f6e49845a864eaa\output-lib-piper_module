{"$message_type":"diagnostic","message":"future cannot be sent between threads safely","code":null,"level":"error","spans":[{"file_name":"piper_module\\src\\streaming.rs","byte_start":1166,"byte_end":1234,"line_start":36,"line_end":36,"column_start":22,"column_end":90,"is_primary":true,"text":[{"text":"        tokio::spawn(Self::audio_playback_task(audio_rx, interrupt_rx, is_speaking_clone));","highlight_start":22,"highlight_end":90}],"label":"future returned by `audio_playback_task` is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"within `impl Future<Output = ()>`, the trait `Send` is not implemented for `*mut ()`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"future is not `Send` as this value is used across an await","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":32898,"byte_end":32903,"line_start":744,"line_end":744,"column_start":16,"column_end":21,"is_primary":true,"text":[{"text":"            }).await","highlight_start":16,"highlight_end":21}],"label":"await occurs here, with `_stream` maybe used later","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":32898,"byte_end":32903,"line_start":744,"line_end":744,"column_start":16,"column_end":21,"is_primary":false,"text":[{"text":"            }).await","highlight_start":16,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":33615,"byte_end":33664,"line_start":766,"line_end":766,"column_start":9,"column_end":58,"is_primary":false,"text":[{"text":"        $crate::select!(@{ start=$start; $($t)*; $else })","highlight_start":9,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":34429,"byte_end":34522,"line_start":778,"line_end":778,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::select!(@{ start=$start; ($($s)* _) $($t)* ($($s)*) $p = $f, if true => $h, } $($r)*)","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":34429,"byte_end":34522,"line_start":778,"line_end":778,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::select!(@{ start=$start; ($($s)* _) $($t)* ($($s)*) $p = $f, if true => $h, } $($r)*)","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":35736,"byte_end":35831,"line_start":806,"line_end":806,"column_start":9,"column_end":104,"is_primary":false,"text":[{"text":"        $crate::select!(@{ start={ $crate::macros::support::thread_rng_n(BRANCHES) }; () } $p = $($t)*)","highlight_start":9,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"piper_module\\src\\streaming.rs","byte_start":6842,"byte_end":7830,"line_start":206,"line_end":232,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            tokio::select! {","highlight_start":13,"highlight_end":29},{"text":"                // New audio chunk received","highlight_start":1,"highlight_end":44},{"text":"                Some(chunk) = audio_rx.recv() => {","highlight_start":1,"highlight_end":51},{"text":"                    // Check for interrupt before playing","highlight_start":1,"highlight_end":58},{"text":"                    if *interrupt_rx.borrow() {","highlight_start":1,"highlight_end":48},{"text":"                        sink.clear();","highlight_start":1,"highlight_end":38},{"text":"                        continue;","highlight_start":1,"highlight_end":34},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                    ","highlight_start":1,"highlight_end":21},{"text":"                    // Convert chunk to playable source","highlight_start":1,"highlight_end":56},{"text":"                    if let Ok(cursor) = Self::chunk_to_source(chunk) {","highlight_start":1,"highlight_end":71},{"text":"                        sink.append(cursor);","highlight_start":1,"highlight_end":45},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                ","highlight_start":1,"highlight_end":17},{"text":"                // Interrupt signal received","highlight_start":1,"highlight_end":45},{"text":"                _ = interrupt_rx.changed() => {","highlight_start":1,"highlight_end":48},{"text":"                    if *interrupt_rx.borrow() {","highlight_start":1,"highlight_end":48},{"text":"                        sink.clear();","highlight_start":1,"highlight_end":38},{"text":"                        let mut speaking = is_speaking.lock().await;","highlight_start":1,"highlight_end":69},{"text":"                        *speaking = false;","highlight_start":1,"highlight_end":43},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                ","highlight_start":1,"highlight_end":17},{"text":"                // Channel closed","highlight_start":1,"highlight_end":34},{"text":"                else => break,","highlight_start":1,"highlight_end":31},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tokio::select!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":24645,"byte_end":24664,"line_start":572,"line_end":572,"column_start":7,"column_end":26,"is_primary":false,"text":[{"text":"doc! {macro_rules! select {","highlight_start":7,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::select!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":24645,"byte_end":24664,"line_start":572,"line_end":572,"column_start":7,"column_end":26,"is_primary":false,"text":[{"text":"doc! {macro_rules! select {","highlight_start":7,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::select!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":24645,"byte_end":24664,"line_start":572,"line_end":572,"column_start":7,"column_end":26,"is_primary":false,"text":[{"text":"doc! {macro_rules! select {","highlight_start":7,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::select!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":24645,"byte_end":24664,"line_start":572,"line_end":572,"column_start":7,"column_end":26,"is_primary":false,"text":[{"text":"doc! {macro_rules! select {","highlight_start":7,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::select!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\macros\\select.rs","byte_start":24645,"byte_end":24664,"line_start":572,"line_end":572,"column_start":7,"column_end":26,"is_primary":false,"text":[{"text":"doc! {macro_rules! select {","highlight_start":7,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"piper_module\\src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"piper_module\\src\\streaming.rs","byte_start":6328,"byte_end":6335,"line_start":189,"line_end":189,"column_start":14,"column_end":21,"is_primary":false,"text":[{"text":"        let (_stream, stream_handle) = match OutputStream::try_default() {","highlight_start":14,"highlight_end":21}],"label":"has type `OutputStream` which is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required by a bound in `tokio::spawn`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\task\\spawn.rs","byte_start":5224,"byte_end":5229,"line_start":166,"line_end":166,"column_start":12,"column_end":17,"is_primary":false,"text":[{"text":"    pub fn spawn<F>(future: F) -> JoinHandle<F::Output>","highlight_start":12,"highlight_end":17}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\task\\spawn.rs","byte_start":5299,"byte_end":5303,"line_start":168,"line_end":168,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"        F: Future + Send + 'static,","highlight_start":21,"highlight_end":25}],"label":"required by this bound in `spawn`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: future cannot be sent between threads safely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mpiper_module\\src\\streaming.rs:36:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mwn(Self::audio_playback_task(audio_rx, interrupt_rx, is_speaking_clone));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfuture returned by `audio_playback_task` is not `Send`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: within `impl Future<Output = ()>`, the trait `Send` is not implemented for `*mut ()`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: future is not `Send` as this value is used across an await\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mpiper_module\\src\\streaming.rs:206:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let (_stream, stream_handle) = match OutputStream::try_\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhas type `OutputStream` which is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            tokio::select! {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                // New audio chunk received\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m208\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Some(chunk) = audio_rx.recv() => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                else => break,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mawait occurs here, with `_stream` maybe used later\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `tokio::spawn`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tokio-1.46.1\\src\\task\\spawn.rs:168:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn spawn<F>(future: F) -> JoinHandle<F::Output>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        F: Future + Send + 'static,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `spawn`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::select` which comes from the expansion of the macro `tokio::select` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Source`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"piper_module\\src\\streaming.rs","byte_start":140,"byte_end":146,"line_start":5,"line_end":5,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"use rodio::{OutputStream, Sink, Source};","highlight_start":33,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Source`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mpiper_module\\src\\streaming.rs:5:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rodio::{OutputStream, Sink, Source};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 1 warning emitted\u001b[0m\n\n"}

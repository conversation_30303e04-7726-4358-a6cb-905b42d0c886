{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 2040997289075261528, "path": 16293370867538934574, "deps": [[2828590642173593838, "cfg_if", false, 6807818261661818368]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\getrandom-ede1acf8d6b21f2e\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
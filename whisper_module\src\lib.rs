use std::path::PathBuf;
use std::process::Stdio;
use std::time::Duration;
use tokio::process::Command as TokioCommand;
use tokio::sync::mpsc::{self, Receiver};
use tokio::io::{AsyncBufReadExt, BufReader as AsyncBufReader};
use tokio::sync::Mutex;
use tokio::task;
use tokio::time;
use std::sync::Arc;

#[derive(Debug, Clone)]
pub struct WhisperConfig {
    pub model_path: PathBuf,
    pub step_ms: u32,      // e.g., 500
    pub length_ms: u32,    // e.g., 2500
    pub threads: u32,      // e.g., 4
    pub language: String,  // e.g., "en"
    pub whisper_bin: PathBuf, // path to whisper-stream binary
}

impl Default for WhisperConfig {
    fn default() -> Self {
        Self {
            model_path: PathBuf::from("whisper.cpp/models/ggml-base.en.bin"),
            step_ms: 800,    // Slightly longer for better accuracy
            length_ms: 3000, // Longer context for better speech detection
            threads: 4,
            language: "en".to_string(),
            whisper_bin: PathBuf::from("whisper.cpp/build/bin/Release/whisper-stream.exe"),
        }
    }
}

#[derive(Debug)]
pub enum WhisperError {
    Io(std::io::Error),
    Process(String),
    StreamNotFound,
}

impl From<std::io::Error> for WhisperError {
    fn from(e: std::io::Error) -> Self {
        WhisperError::Io(e)
    }
}

/// Starts whisper-stream and returns a channel receiver for real-time transcriptions.
pub async fn start_streaming(config: WhisperConfig) -> Result<Receiver<String>, WhisperError> {
    start_streaming_with_tts_filter(config, None).await
}

/// Starts whisper-stream with optional TTS feedback filtering.
/// If tts_receiver is provided, TTS outputs will be filtered from transcriptions.
pub async fn start_streaming_with_tts_filter(
    config: WhisperConfig,
    tts_receiver: Option<Receiver<String>>
) -> Result<Receiver<String>, WhisperError> {
    // Check if binary exists
    if !config.whisper_bin.exists() {
        return Err(WhisperError::StreamNotFound);
    }

    // Build command
    let mut cmd = TokioCommand::new(&config.whisper_bin);
    cmd.arg("-m").arg(&config.model_path)
        .arg("-t").arg(config.threads.to_string())
        .arg("--step").arg(config.step_ms.to_string())
        .arg("--length").arg(config.length_ms.to_string())
        .arg("-l").arg(&config.language)
        .arg("--vad-thold").arg("0.3")    // Much less sensitive to noise
        .arg("--freq-thold").arg("200.0") // Focus more on speech frequencies
        .arg("--keep").arg("3000")       // Reasonable context
        .arg("--max-tokens").arg("32")   // Allow longer phrases
        .arg("--audio-ctx").arg("512")   // Better transcription quality
        .arg("-c").arg("0")              // Verify this is your microphone
        .arg("--keep-context")           // Continuity
        .arg("--print-special")          // Debugging
        .stdout(Stdio::piped())
        .stderr(Stdio::piped());
    println!("🎤 Starting Whisper with command: {:?}", cmd);

    // Spawn the process
    let mut child = cmd.spawn().map_err(|e| {
        eprintln!("❌ Failed to spawn Whisper process: {:?}", e);
        WhisperError::Io(e)
    })?;

    // Capture stdout and stderr *before* moving child
    let stdout = child.stdout.take().ok_or_else(|| WhisperError::Process("Failed to capture stdout".to_string()))?;
    let stderr = child.stderr.take().ok_or_else(|| WhisperError::Process("Failed to capture stderr".to_string()))?;

    // Monitor child process after taking stdout/stderr
    let _child_id = child.id();
    task::spawn(async move {
        if let Ok(status) = child.wait().await {
            println!("🎤 Whisper process exited with status: {:?}", status);
        } else {
            eprintln!("❌ Whisper process wait failed");
        }
    });

    // Set up readers for stdout and stderr
    let reader = AsyncBufReader::new(stdout);
    let error_reader = AsyncBufReader::new(stderr);
    let mut lines = reader.lines();
    let mut error_lines = error_reader.lines();
    let (tx, rx) = mpsc::channel(32);

    // Store recent TTS outputs to filter feedback
    let recent_tts: Arc<Mutex<Vec<String>>> = Arc::new(Mutex::new(Vec::new()));

    // Spawn task to read stderr for debugging
    task::spawn(async move {
        while let Ok(Some(line)) = error_lines.next_line().await {
            if !line.trim().is_empty() {
                eprintln!("🎤 Whisper stderr: {}", line);
            }
        }
    });

    // Handle TTS feedback filtering if receiver is provided
    let recent_tts_clone = Arc::clone(&recent_tts);
    if let Some(mut tts_receiver) = tts_receiver {
        task::spawn(async move {
            while let Some(tts_text) = tts_receiver.recv().await {
                println!("🔇 Adding TTS to filter: '{}'", tts_text);
                let mut tts_buffer = recent_tts_clone.lock().await;
                tts_buffer.push(tts_text.clone());
                if tts_buffer.len() > 5 {
                    tts_buffer.remove(0); // Keep buffer small
                }
                drop(tts_buffer);

                // Remove after 10 seconds
                let recent_tts_clone = Arc::clone(&recent_tts_clone);
                let tts_clone = tts_text.clone();
                task::spawn(async move {
                    time::sleep(Duration::from_secs(10)).await;
                    let mut tts_buffer = recent_tts_clone.lock().await;
                    if let Some(pos) = tts_buffer.iter().position(|x| x == &tts_clone) {
                        tts_buffer.remove(pos);
                    }
                    println!("🔇 Removed TTS from filter: '{}'", tts_clone);
                });
            }
        });
    } else {
        // Fallback: Add some common TTS phrases to filter
        task::spawn(async move {
            let common_tts_phrases = vec![
                "honey", "babe", "love", "sweetie", "darling",
                "How can I help", "What can I do", "I'm here for you",
                "Okay", "Aww", "Oh", "Whoa", "Oops"
            ];
            for phrase in common_tts_phrases {
                let mut tts_buffer = recent_tts_clone.lock().await;
                tts_buffer.push(phrase.to_string());
            }
        });
    }

    // Spawn task to read stdout and send valid transcriptions
    let tx_clone = tx.clone();
    let recent_tts_clone = Arc::clone(&recent_tts);
    task::spawn(async move {
        println!("🎤 Whisper stdout reader started");
        while let Ok(Some(line)) = lines.next_line().await {
            println!("🎤 Raw Whisper output: '{}'", line);
            let trimmed = line.trim();
            // Check against recent TTS outputs with sophisticated matching
            let is_tts_feedback = {
                let tts_buffer = recent_tts_clone.lock().await;
                tts_buffer.iter().any(|tts| {
                    // Exact match
                    if trimmed == tts || tts == trimmed {
                        return true;
                    }
                    // Substring match (both directions)
                    if trimmed.len() > 10 && tts.len() > 10 {
                        if trimmed.contains(tts) || tts.contains(trimmed) {
                            return true;
                        }
                    }
                    // Word overlap check for longer phrases
                    if trimmed.len() > 20 && tts.len() > 20 {
                        let trimmed_words: Vec<&str> = trimmed.split_whitespace().collect();
                        let tts_words: Vec<&str> = tts.split_whitespace().collect();
                        let overlap = trimmed_words.iter()
                            .filter(|word| tts_words.contains(word))
                            .count();
                        // If more than 60% of words overlap, consider it TTS feedback
                        overlap as f32 / trimmed_words.len().min(tts_words.len()) as f32 > 0.6
                    } else {
                        false
                    }
                })
            };
            // Check for repetitive characters (like "( ( ( ( (")
            let has_repetitive_chars = {
                let chars: Vec<char> = trimmed.chars().collect();
                if chars.len() > 5 {
                    let mut repetitive_count = 0;
                    for i in 0..chars.len()-1 {
                        if chars[i] == chars[i+1] && (chars[i] == '(' || chars[i] == ')' || chars[i] == ' ') {
                            repetitive_count += 1;
                        }
                    }
                    repetitive_count > chars.len() / 3 // More than 1/3 repetitive
                } else {
                    false
                }
            };

            if !trimmed.is_empty() &&
               !trimmed.contains("[Start speaking]") &&
               !trimmed.contains("[BLANK_AUDIO]") &&
               !trimmed.contains("whisper_print_timings") &&
               !trimmed.starts_with("whisper_") &&
               !trimmed.contains("[inaudible]") &&
               !trimmed.contains("[_TT_") &&
               !trimmed.contains("[_BEG_]") &&
               !trimmed.contains("<|endoftext|>") &&
               !trimmed.contains("(inaudible)") &&
               !trimmed.starts_with("[_") &&
               !trimmed.ends_with("_]") &&
               !has_repetitive_chars &&
               trimmed.len() >= 20 &&
               !is_tts_feedback {
                println!("🗣️ Sending voice input: '{}'", trimmed);
                let _ = tx_clone.send(trimmed.to_string()).await;
            } else {
                println!("🎤 Skipping invalid output: '{}'", trimmed);
            }
        }
        println!("🎤 Whisper stdout reader ended");
    });

    Ok(rx)
}

/// Test microphone input by running a short Whisper session
pub async fn test_microphone(config: WhisperConfig) -> Result<(), WhisperError> {
    println!("🎤 Testing microphone input...");
    println!("🎤 Speak clearly for 5 seconds...");

    // Check if binary exists
    if !config.whisper_bin.exists() {
        return Err(WhisperError::StreamNotFound);
    }

    // Build test command with more verbose output
    let mut cmd = TokioCommand::new(&config.whisper_bin);
    cmd.arg("-m").arg(&config.model_path)
        .arg("-t").arg("2")  // Fewer threads for test
        .arg("--step").arg("1000")  // 1 second steps
        .arg("--length").arg("5000") // 5 second test
        .arg("-l").arg(&config.language)
        .arg("--vad-thold").arg("0.2")  // Moderate sensitivity for test
        .arg("--freq-thold").arg("100.0")
        .arg("-c").arg("0")  // Default microphone
        .arg("--print-special")
        .stdout(Stdio::piped())
        .stderr(Stdio::piped());

    println!("🎤 Running test command: {:?}", cmd);

    // Spawn the process
    let mut child = cmd.spawn().map_err(|e| {
        eprintln!("❌ Failed to spawn Whisper test process: {:?}", e);
        WhisperError::Io(e)
    })?;

    // Wait for completion with timeout
    let child_id = child.id();
    let result = tokio::time::timeout(
        Duration::from_secs(10),
        child.wait_with_output()
    ).await;

    match result {
        Ok(Ok(output)) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);

            println!("🎤 Test completed!");
            println!("📤 Stdout: {}", stdout);
            if !stderr.is_empty() {
                println!("📤 Stderr: {}", stderr);
            }

            if output.status.success() {
                println!("✅ Microphone test successful!");
            } else {
                println!("⚠️ Microphone test completed with warnings");
            }
            Ok(())
        }
        Ok(Err(e)) => {
            eprintln!("❌ Test process error: {:?}", e);
            Err(WhisperError::Io(e))
        }
        Err(_) => {
            eprintln!("❌ Test timed out (process ID: {:?})", child_id);
            // Note: child is already consumed by wait_with_output, so we can't kill it here
            Err(WhisperError::Process("Test timed out".to_string()))
        }
    }
}